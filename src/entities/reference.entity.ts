import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, ManyTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { Module } from './module.entity';
import { Lesson } from './lesson.entity';

@Entity('references')
export class Reference {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column()
  file_url: string;

  @Column()
  type: string;

  @Column({ nullable: true })
  module_id: string;

  @Column({ nullable: true })
  lesson_id: string;

  @ManyToOne(() => Module, { nullable: true })
  @JoinColumn({ name: 'module_id' })
  module: Module;

  @ManyToOne(() => Lesson, { nullable: true })
  @JoinColumn({ name: 'lesson_id' })
  lesson: Lesson;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updated_at: Date;
} 