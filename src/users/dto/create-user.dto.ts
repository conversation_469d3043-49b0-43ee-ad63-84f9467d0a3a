import { IsEmail, IsEnum, IsNotEmpty, IsO<PERSON>al, IsString, <PERSON><PERSON><PERSON><PERSON> } from "class-validator"
import { UserRole } from "../entities/user.entity"

export class CreateUserDto {
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  name: string

  @IsNotEmpty()
  @IsEmail()
  email: string

  @IsNotEmpty()
  @IsString()
  @MinLength(6)
  password: string

  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole
}
