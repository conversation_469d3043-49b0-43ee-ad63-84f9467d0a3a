import { IsEmail, IsEnum, IsO<PERSON>al, IsString, <PERSON><PERSON><PERSON><PERSON> } from "class-validator"
import { UserRole } from "../entities/user.entity"

export class UpdateUserDto {
  @IsOptional()
  @IsString()
  @MinLength(2)
  name?: string

  @IsOptional()
  @IsEmail()
  email?: string

  @IsOptional()
  @IsString()
  @MinLength(6)
  password?: string

  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole
}
