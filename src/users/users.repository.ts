import { Injectable } from "@nestjs/common"
import { InjectRepository } from "@nestjs/typeorm"
import type { Repository } from "typeorm"
import type { CreateUserDto } from "./dto/create-user.dto"
import type { UpdateUserDto } from "./dto/update-user.dto"
import { User } from "./entities/user.entity"

@Injectable()
export class UsersRepository {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto)
    return await this.userRepository.save(user)
  }

  async findAll(): Promise<User[]> {
    return await this.userRepository.find({
      select: ["id", "name", "email", "role", "createdAt", "updatedAt"],
    })
  }

  async findById(id: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { id },
      select: ["id", "name", "email", "role", "createdAt", "updatedAt"],
    })
  }

  async findByEmail(email: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { email },
    })
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User | null> {
    await this.userRepository.update(id, updateUserDto)
    return await this.findById(id)
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.userRepository.delete(id)
    return (result.affected ?? 0) > 0
  }

  async exists(id: string): Promise<boolean> {
    const count = await this.userRepository.count({ where: { id } })
    return count > 0
  }

  async emailExists(email: string, excludeId?: string): Promise<boolean> {
    const query = this.userRepository.createQueryBuilder("user").where("user.email = :email", { email })

    if (excludeId) {
      query.andWhere("user.id != :excludeId", { excludeId })
    }

    const count = await query.getCount()
    return count > 0
  }
}
